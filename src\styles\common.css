:root {
  --font-family: "DM Sans", "Segoe UI", "Roboto", "Helvetica Neue", "Arial",
    "sans-serif";
  --color-purple-opacity: 130, 117, 247;
  --color-purple-300: 92, 0, 250;
  --color-purple-200: 82, 11, 240;
  --color-green-500: 51, 192, 142;
  --color-green-400: 128, 212, 89;
  --color-green-300: 14, 217, 9;
  --color-green-200: 225, 246, 225;
  --color-green-100: var(--color-green-500);
  --color-green-5: 14, 217, 9;

  --color-dark-green-300: 10, 182, 121;

  --color-negative-100: 255, 53, 40;
  --color-negative-200: 234, 53, 41;

  --color-orange-300: 255, 156, 40;
  --color-orange-200: 249, 239, 227;
  --color-orange-5: 255, 156, 40;

  --color-grey-100: 248, 248, 248;
  --color-grey-opacity: 9, 8, 31;

  --color-yellow-300: 251, 255, 47;

  --color-white: 255, 255, 255;
  --color-black: 9, 8, 31;
  --color-black-opacity: 1;

  --color-element: var(--color-black);
  --color-surface: var(--color-white);
  --color-brand: var(--color-purple-opacity);
  --color-alert: var(--color-orange-300);
  --color-positive: var(--color-green-300);
  --color-negative: var(--color-negative-100);

  --color-oklch-100: 97% .001 286.4;

  --dt-color-element-2: rgba(var(--color-element), 0.02);
  --dt-color-element-5: rgba(var(--color-element), 0.05);
  --dt-color-element-10: rgba(var(--color-element), 0.1);
  --dt-color-element-20: rgba(var(--color-element), 0.2);
  --dt-color-element-50: rgba(var(--color-element), 0.5);
  --dt-color-element-75: rgba(var(--color-element), 0.75);
  --dt-color-element-100: rgba(var(--color-element), 1);

  --dt-color-brand-5: rgba(var(--color-brand), 0.05);
  --dt-color-brand-10: rgba(var(--color-brand), 0.1);
  --dt-color-brand-40: rgba(var(--color-brand), 0.4);
  --dt-color-brand-50: rgba(var(--color-brand), 0.5);
  --dt-color-brand-60: rgba(var(--color-brand), 0.6);
  --dt-color-brand-100: rgba(var(--color-brand), 1);

  --dt-color-surface-0: rgba(var(--color-surface), 0);
  --dt-color-surface-15: rgba(var(--color-surface), 0.15);
  --dt-color-surface-100: rgba(var(--color-surface), 1);

  --dt-color-alert-5: rgba(var(--color-alert), 0.05);
  --dt-color-alert-100: rgba(var(--color-alert), 1);

  --dt-color-positive-5: rgba(var(--color-positive), 0.05);
  --dt-color-positive-100: rgba(var(--color-positive), 1);

  --dt-color-negative-5: rgba(var(--color-negative), 0.05);
  --dt-color-negative-100: rgba(var(--color-negative), 1);
}

body {
  /* never put background color here */
  margin: 0;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

:focus-visible {
  outline: none !important;
}

@layer base {
  a {
    @apply text-purple-300;
  }

  a:hover {
    @apply text-purple-400;
  }
}

.text-error {
  @apply text-negative-100 text-xs min-h-4 leading-4;
}

/* override autofill and onepassword */
input[data-com-onepassword-filled="light"],
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
input:is(
    :-webkit-autofill,
    :autofill,
    :-webkit-autofill:first-line,
    :autofill:first-line
  ) {
  font-family: inherit !important;
  color: inherit !important;
  -webkit-text-fill-color: inherit !important;
  background-color: transparent !important;
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  box-shadow: 0 0 0 30px white inset !important;
  transition: background-color 5000s ease-in-out 0s;
}

.d-h-screen {
  @apply h-dvh;
}

.no-scrollbar {
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
}
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.scrollbar::-webkit-scrollbar {
  width: 2px;
  height: 2px;
  border-radius: 9999px;
}

.scrollbar::-webkit-scrollbar-thumb {
  background: rgba(var(--color-black), 0.05);
  border-radius: 9999px;
}

.scrollbar::-webkit-scrollbar-track {
  background: rgba(var(--color-white), 1);
}

.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinner {
  -moz-appearance: textfield;
}


.bubble-message li{
  line-height: 1.8;
}

/* Hide textarea auto-resize element on mobile */
@media (max-width: 640px) {
  .bubble-input-container::after {
    display: none;
  }
}

/* Color picker optimizations for better drag performance */
.react-colorful,
.chrome-picker,
[class*="chrome-picker"] {
  touch-action: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Optimize color picker saturation and hue areas for smooth dragging */
.react-colorful__saturation,
.react-colorful__hue,
.chrome-picker .saturation-white,
.chrome-picker .saturation-black,
.chrome-picker .hue-horizontal {
  will-change: transform !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
}

/* Prevent text selection and improve performance on color picker pointers */
.react-colorful__pointer,
.chrome-picker .saturation-white > div,
.chrome-picker .hue-horizontal > div {
  will-change: transform !important;
  transform: translateZ(0) !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  pointer-events: auto !important;
}

/* Mobile-specific optimizations for color picker */
@media (max-width: 640px) {
  .chrome-picker {
    max-width: calc(100vw - 32px) !important;
    width: auto !important;
  }

  /* Increase touch target sizes on mobile */
  .chrome-picker .saturation-white > div,
  .chrome-picker .hue-horizontal > div,
  .react-colorful__pointer {
    width: 20px !important;
    height: 20px !important;
    border-width: 3px !important;
  }

  /* Improve visibility of color picker elements on mobile */
  .chrome-picker .saturation-white > div {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(0, 0, 0, 0.3) !important;
  }

  .chrome-picker .hue-horizontal > div {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(0, 0, 0, 0.3) !important;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .chrome-picker .saturation-white > div,
  .chrome-picker .hue-horizontal > div {
    width: 16px !important;
    height: 16px !important;
    border-width: 2px !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chrome-picker .saturation-white,
  .chrome-picker .saturation-black,
  .chrome-picker .hue-horizontal {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}

/* Prevent zoom on double tap for color picker on iOS */
.chrome-picker,
.chrome-picker * {
  touch-action: manipulation !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
}

/* Improve color picker input fields on mobile */
@media (max-width: 640px) {
  .chrome-picker input {
    font-size: 16px !important; /* Prevent zoom on iOS */
    padding: 8px !important;
    border-radius: 4px !important;
    min-height: 44px !important; /* iOS recommended touch target size */
  }

  /* Ensure color picker doesn't get cut off on small screens */
  .chrome-picker {
    transform: scale(0.9) !important;
    transform-origin: top left !important;
  }
}

/* Landscape orientation optimizations for mobile */
@media (max-width: 640px) and (orientation: landscape) {
  .chrome-picker {
    transform: scale(0.8) !important;
    max-height: calc(100vh - 60px) !important;
  }
}
